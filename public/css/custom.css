/* Custom CSS for Job Board */

/* General Styles */
body {
    font-family: 'Poppins', sans-serif;
    color: #333;
    line-height: 1.6;
    background-color: #f8f9fa;
}

a {
    color: #0d6efd;
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: #0a58ca;
}

.btn {
    border-radius: 5px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0a58ca;
    border-color: #0a58ca;
}

.btn-outline-primary {
    color: #0d6efd;
    border-color: #0d6efd;
}

.btn-outline-primary:hover {
    background-color: #0d6efd;
    color: #fff;
}

.section-title {
    position: relative;
    margin-bottom: 30px;
    padding-bottom: 15px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #0d6efd;
}

/* Header Styles */
.navbar {
    padding: 15px 0;
}

.navbar-brand {
    font-size: 24px;
}

.nav-link {
    font-weight: 500;
    padding: 8px 15px !important;
}

.nav-link.active {
    color: #0d6efd !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(rgba(13, 110, 253, 0.8), rgba(13, 110, 253, 0.9)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    color: #fff;
    padding: 100px 0;
    margin-bottom: 50px;
}

.hero-section h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
}

.hero-section p {
    font-size: 18px;
    margin-bottom: 30px;
}

.search-form {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 30px;
    border-radius: 10px;
}

/* Job Card Styles */
.job-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 25px;
    margin-bottom: 25px;
    transition: all 0.3s ease;
    border-left: 4px solid #0d6efd;
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.job-card .company-logo {
    width: 70px;
    height: 70px;
    object-fit: contain;
    border-radius: 5px;
    background-color: #f8f9fa;
    padding: 10px;
}

.job-card .job-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
}

.job-card .company-name {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 15px;
}

.job-card .job-details {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.job-card .job-detail {
    margin-right: 20px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #6c757d;
}

.job-card .job-detail i {
    margin-right: 5px;
    color: #0d6efd;
}

.job-card .job-tags {
    margin-bottom: 15px;
}

.job-card .job-tag {
    display: inline-block;
    background-color: #e9ecef;
    color: #495057;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    margin-right: 10px;
    margin-bottom: 10px;
}

.job-card .job-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.job-card .job-deadline {
    font-size: 14px;
    color: #dc3545;
}

/* Featured Employers Section */
.employer-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 25px;
    margin-bottom: 25px;
    text-align: center;
    transition: all 0.3s ease;
}

.employer-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.employer-card .employer-logo {
    width: 100px;
    height: 100px;
    object-fit: contain;
    margin: 0 auto 20px;
}

.employer-card .employer-name {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

.employer-card .job-count {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 15px;
}

/* Job Detail Page */
.job-detail-header {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
    margin-bottom: 30px;
}

.job-detail-header .company-logo {
    width: 100px;
    height: 100px;
    object-fit: contain;
    border-radius: 5px;
    background-color: #f8f9fa;
    padding: 10px;
}

.job-detail-content {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
}

.job-detail-sidebar {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
}

/* Dashboard Styles */
.dashboard-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 25px;
    margin-bottom: 25px;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.dashboard-card .card-icon {
    width: 60px;
    height: 60px;
    background-color: rgba(13, 110, 253, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.dashboard-card .card-icon i {
    font-size: 24px;
    color: #0d6efd;
}

.dashboard-card .card-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
}

.dashboard-card .card-value {
    font-size: 28px;
    font-weight: 700;
    color: #0d6efd;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .hero-section {
        padding: 70px 0;
    }
    
    .hero-section h1 {
        font-size: 36px;
    }
    
    .search-form {
        padding: 20px;
    }
    
    .job-card .job-actions {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .job-card .job-deadline {
        margin-top: 10px;
    }
}
