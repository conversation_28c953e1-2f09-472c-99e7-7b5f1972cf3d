/* Employer Dashboard Styles */

.dashboard-container {
    padding: 40px 0;
}

.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-header h1 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 10px;
}

.dashboard-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-bottom: 30px;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.dashboard-card .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dashboard-card .card-header h5 {
    font-weight: 600;
    margin: 0;
}

.dashboard-card .card-body {
    padding: 25px;
}

.profile-summary {
    text-align: center;
    padding: 20px;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    font-weight: 600;
    color: white;
    background-color: #0d6efd;
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.2);
}

.profile-logo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 auto 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-name {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
}

.profile-email {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 14px;
}

.stats-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stats-item:last-child {
    border-bottom: none;
}

.stats-label {
    font-weight: 500;
}

.stats-value {
    font-weight: 600;
}

.welcome-card {
    background: linear-gradient(135deg, #0d6efd, #0a58ca);
    color: white;
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 30px;
}

.welcome-card h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
}

.welcome-card p {
    margin-bottom: 20px;
    opacity: 0.9;
}

.welcome-card .btn {
    background-color: white;
    color: #0d6efd;
    border: none;
    font-weight: 600;
}

.welcome-card .btn:hover {
    background-color: rgba(255, 255, 255, 0.9);
}

.job-table th {
    font-weight: 600;
    color: #495057;
}

.job-table td {
    vertical-align: middle;
}

.job-status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-approved {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-pending {
    background-color: #fff3cd;
    color: #664d03;
}

.job-card {
    height: 100%;
    border: none;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.job-card .card-title {
    font-size: 18px;
    font-weight: 600;
}

.job-card .card-subtitle {
    font-size: 14px;
}

.job-card .badge {
    font-weight: 500;
    padding: 5px 10px;
    margin-right: 5px;
    margin-bottom: 5px;
}

.job-card .card-text {
    margin-bottom: 15px;
    color: #6c757d;
}

.action-buttons .dropdown-item {
    display: flex;
    align-items: center;
}

.action-buttons .dropdown-item i {
    width: 20px;
    margin-right: 8px;
}

.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.quick-action-card {
    flex: 1;
    min-width: 200px;
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.quick-action-card:hover {
    background-color: #e9ecef;
    transform: translateY(-5px);
}

.quick-action-icon {
    font-size: 24px;
    margin-bottom: 10px;
    color: #0d6efd;
}

.quick-action-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.quick-action-description {
    font-size: 14px;
    color: #6c757d;
}

.application-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: #0d6efd;
    color: white;
    border-radius: 50%;
    font-size: 12px;
    font-weight: 600;
    margin-left: 5px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 20px 0;
    }
    
    .welcome-card {
        padding: 20px;
    }
    
    .welcome-card h2 {
        font-size: 20px;
    }
    
    .quick-action-card {
        min-width: 100%;
    }
}
