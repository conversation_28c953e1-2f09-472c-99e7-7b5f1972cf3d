/* Profile Page Styles */

.profile-container {
    padding: 40px 0;
}

.profile-header {
    margin-bottom: 30px;
}

.profile-header h1 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 10px;
}

.profile-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-bottom: 30px;
    overflow: hidden;
}

.profile-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.profile-card .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.profile-card .card-header h5 {
    font-weight: 600;
    margin: 0;
}

.profile-card .card-body {
    padding: 25px;
}

.profile-image-container {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto 20px;
}

.profile-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid #f8f9fa;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.avatar-placeholder {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    font-weight: 600;
    background-color: #f8f9fa;
    color: #0d6efd;
    border: 5px solid #f8f9fa;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-image-edit {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: #0d6efd;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.profile-image-edit:hover {
    background-color: #0a58ca;
    transform: scale(1.1);
}

.profile-name {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
}

.profile-email {
    color: #6c757d;
    margin-bottom: 15px;
}

.profile-info {
    margin-bottom: 20px;
}

.profile-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.profile-info-item i {
    width: 25px;
    color: #0d6efd;
    margin-right: 10px;
}

.profile-actions {
    margin-top: 20px;
}

.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.skill-badge {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.skill-badge:hover {
    background-color: #0d6efd;
    color: white;
}

.form-section {
    margin-bottom: 30px;
}

.form-section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.custom-file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%;
}

.custom-file-upload input[type="file"] {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.custom-file-upload .file-upload-btn {
    display: block;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border: 1px dashed #ced4da;
    border-radius: 5px;
    text-align: center;
    transition: all 0.3s ease;
}

.custom-file-upload:hover .file-upload-btn {
    background-color: #e9ecef;
    border-color: #0d6efd;
}

.custom-file-upload .file-upload-text {
    margin-top: 5px;
    font-size: 14px;
    color: #6c757d;
}

/* Responsive styles */
@media (max-width: 768px) {
    .profile-container {
        padding: 20px 0;
    }
    
    .profile-image-container {
        width: 120px;
        height: 120px;
    }
    
    .profile-image, .avatar-placeholder {
        width: 120px;
        height: 120px;
    }
    
    .profile-name {
        font-size: 20px;
    }
}
